# Server Configuration
PORT=3000

# MongoDB Atlas Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/database_name

# Replicate AI Configuration
AI_KEY=your_replicate_api_key_here

# ImageKit Configuration
IMAGEKIT_PUBLIC_KEY=your_imagekit_public_key_here
IMAGEKIT_PRIVATE_KEY=your_imagekit_private_key_here
IMAGEKIT_URL_ENDPOINT=https://ik.imagekit.io/your_imagekit_id/

# Instructions:
# 1. Copy this file to .env
# 2. Replace all placeholder values with your actual credentials
# 3. Never commit the .env file to version control
#
# To get ImageKit credentials:
# 1. Sign up at https://imagekit.io/
# 2. Go to Developer section in your dashboard
# 3. Copy your Public Key, Private Key, and URL Endpoint
