require('dotenv').config();
const mongoose = require('mongoose');
const Image = require('./models/Image');

// MongoDB Atlas connection
const mongoURI = process.env.MONGODB_URI ;

async function addTestImages() {
  try {
    await mongoose.connect(mongoURI);
    console.log('✅ Connected to MongoDB Atlas');

    // Sample test images
    const testImages = [
      {
        prompt: "A beautiful sunset over mountains with orange and pink sky",
        imageUrl: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800",
        aspectRatio: "16:9",
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2 hours ago
      },
      {
        prompt: "Modern logo design for tech company with blue gradient",
        imageUrl: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800",
        aspectRatio: "1:1",
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24) // 1 day ago
      },
      {
        prompt: "Portrait of a smiling person in professional attire",
        imageUrl: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800",
        aspectRatio: "9:16",
        createdAt: new Date(Date.now() - 1000 * 60 * 30) // 30 minutes ago
      },
      {
        prompt: "Nature landscape with green forest and flowing river",
        imageUrl: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800",
        aspectRatio: "4:3",
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3) // 3 days ago
      },
      {
        prompt: "Creative poster design for music festival with vibrant colors",
        imageUrl: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800",
        aspectRatio: "3:4",
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 6) // 6 hours ago
      },
      {
        prompt: "Minimalist t-shirt design with geometric patterns",
        imageUrl: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=800",
        aspectRatio: "1:1",
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 12) // 12 hours ago
      },
      {
        prompt: "Group of people working together in modern office",
        imageUrl: "https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=800",
        aspectRatio: "16:9",
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7) // 1 week ago
      },
      {
        prompt: "Abstract logo with circular design and gradient colors",
        imageUrl: "https://images.unsplash.com/photo-1558655146-d09347e92766?w=800",
        aspectRatio: "1:1",
        createdAt: new Date(Date.now() - 1000 * 60 * 15) // 15 minutes ago
      }
    ];

    // Clear existing test images (optional)
    await Image.deleteMany({});

    // Insert test images
    await Image.insertMany(testImages);

  } catch (error) {
    console.error('❌ Error adding test images:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run the script
addTestImages();
