require('dotenv').config();
const ImageKit = require('imagekit');

async function testImageKit() {
    try {
        console.log('🔗 Testing ImageKit connection...');
        
        // Check if all required environment variables are set
        const requiredVars = ['IMAGEKIT_PUBLIC_KEY', 'IMAGEKIT_PRIVATE_KEY', 'IMAGEKIT_URL_ENDPOINT'];
        const missingVars = requiredVars.filter(varName => !process.env[varName]);
        
        if (missingVars.length > 0) {
            console.error('❌ Missing required environment variables:', missingVars.join(', '));
            console.log('💡 Please check your .env file and ensure all ImageKit credentials are set.');
            return;
        }

        // Initialize ImageKit
        const imagekit = new ImageKit({
            publicKey: process.env.IMAGEKIT_PUBLIC_KEY,
            privateKey: process.env.IMAGEKIT_PRIVATE_KEY,
            urlEndpoint: process.env.IMAGEKIT_URL_ENDPOINT
        });

        console.log('✅ ImageKit initialized successfully!');
        console.log('🔗 URL Endpoint:', process.env.IMAGEKIT_URL_ENDPOINT);
        
        // Test authentication by trying to get authentication parameters
        try {
            const authParams = imagekit.getAuthenticationParameters();
            console.log('✅ Authentication test passed!');
            console.log('🔑 Generated token:', authParams.token.substring(0, 10) + '...');
        } catch (authError) {
            console.error('❌ Authentication test failed:', authError.message);
        }

        // Test URL generation
        try {
            const testUrl = imagekit.url({
                path: '/test-image.jpg',
                transformation: [{
                    height: 300,
                    width: 400
                }]
            });
            console.log('✅ URL generation test passed!');
            console.log('🔗 Sample URL:', testUrl);
        } catch (urlError) {
            console.error('❌ URL generation test failed:', urlError.message);
        }

        console.log('\n🎉 ImageKit setup is ready for use!');
        console.log('💡 You can now generate images and they will be automatically uploaded to ImageKit.');

    } catch (error) {
        console.error('❌ ImageKit test failed:', error.message);
        console.log('\n🔧 Troubleshooting:');
        console.log('1. Check your ImageKit credentials in the .env file');
        console.log('2. Ensure your ImageKit account is active');
        console.log('3. Verify your URL endpoint format: https://ik.imagekit.io/your_imagekit_id/');
    }
}

testImageKit();
