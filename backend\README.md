# Gen Free AI Backend

Backend server for the Gen Free AI image generation platform using Node.js, Express, and MongoDB Atlas.

## Features

- 🎨 AI Image Generation API
- 🗄️ MongoDB Atlas Database Integration
- 🔐 Environment-based Configuration
- 📊 Admin Panel API
- 🌐 CORS Support
- ⚡ Express.js Framework

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- MongoDB Atlas account
- Replicate API key (for AI image generation)
- ImageKit account (for permanent image storage)

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Configuration

1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Edit the `.env` file with your actual credentials:
```env
# Server Configuration
PORT=3000

# MongoDB Atlas Configuration
MONGODB_URI=*************************************************************************************

# Replicate AI Configuration
AI_KEY=your_actual_replicate_api_key

# ImageKit Configuration
IMAGEKIT_PUBLIC_KEY=your_imagekit_public_key
IMAGEKIT_PRIVATE_KEY=your_imagekit_private_key
IMAGEKIT_URL_ENDPOINT=https://ik.imagekit.io/your_imagekit_id/
```

### 3. MongoDB Atlas Setup

1. **Create MongoDB Atlas Account**:
   - Go to [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
   - Sign up for a free account

2. **Create a Cluster**:
   - Create a new cluster (free tier available)
   - Choose your preferred cloud provider and region

3. **Create Database User**:
   - Go to Database Access
   - Add a new database user with read/write permissions
   - Remember the username and password

4. **Configure Network Access**:
   - Go to Network Access
   - Add your IP address to the whitelist
   - For development, you can use `0.0.0.0/0` (not recommended for production)

5. **Get Connection String**:
   - Go to Clusters → Connect → Connect your application
   - Copy the connection string
   - Replace `<password>` with your database user password
   - Replace `<dbname>` with your database name

### 4. Replicate API Setup

1. Go to [Replicate](https://replicate.com/)
2. Sign up and get your API token
3. Add the token to your `.env` file as `AI_KEY`

### 5. ImageKit Setup

1. **Create ImageKit Account**:
   - Go to [ImageKit.io](https://imagekit.io/)
   - Sign up for a free account

2. **Get API Credentials**:
   - Go to your ImageKit Dashboard
   - Navigate to Developer section
   - Copy your Public Key, Private Key, and URL Endpoint

3. **Add to Environment Variables**:
   ```env
   IMAGEKIT_PUBLIC_KEY=your_public_key_here
   IMAGEKIT_PRIVATE_KEY=your_private_key_here
   IMAGEKIT_URL_ENDPOINT=https://ik.imagekit.io/your_imagekit_id/
   ```

## Running the Server

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

The server will start on `http://localhost:8000` (or the port specified in your `.env` file).

## Testing Database Connection

Test your MongoDB Atlas connection:
```bash
npm run test-db
```

Test admin functionality:
```bash
npm run test-admin
```

Test ImageKit integration:
```bash
npm run test-imagekit
```

## API Endpoints

### Image Generation
- `POST /api/ai/generate` - Generate AI images

### Admin Panel
- `GET /api/admin/stats` - Get admin statistics
- `POST /api/admin/action` - Perform admin actions

## Project Structure

```
backend/
├── src/
│   ├── controllers/     # Route controllers
│   ├── models/         # MongoDB models
│   ├── routers/        # Express routers
│   └── middleware/     # Custom middleware
├── app.js              # Express app configuration
├── server.js           # Server startup
├── .env.example        # Environment variables template
├── .gitignore          # Git ignore rules
└── package.json        # Dependencies and scripts
```

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `PORT` | Server port number | No (default: 3000) |
| `MONGODB_URI` | MongoDB Atlas connection string | Yes |
| `AI_KEY` | Replicate API key for image generation | Yes |
| `IMAGEKIT_PUBLIC_KEY` | ImageKit public API key | Yes |
| `IMAGEKIT_PRIVATE_KEY` | ImageKit private API key | Yes |
| `IMAGEKIT_URL_ENDPOINT` | ImageKit URL endpoint | Yes |

## Troubleshooting

### MongoDB Connection Issues

1. **Check your connection string**: Ensure the MONGODB_URI is correctly formatted
2. **Verify credentials**: Make sure username and password are correct
3. **Network access**: Ensure your IP is whitelisted in MongoDB Atlas
4. **Cluster status**: Check if your cluster is running and not paused

### Common Error Messages

- `MONGODB_URI environment variable is not set`: Add MONGODB_URI to your .env file
- `Authentication failed`: Check your database user credentials
- `Network timeout`: Check your network access settings in MongoDB Atlas
- `Database not found`: Ensure your database name is correct in the connection string

## Security Notes

- Never commit your `.env` file to version control
- Use strong passwords for your MongoDB Atlas database users
- Restrict network access to specific IP addresses in production
- Keep your API keys secure and rotate them regularly

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Verify your environment configuration
3. Test your database connection using the provided test scripts

## License

This project is part of the Gen Free AI platform.
