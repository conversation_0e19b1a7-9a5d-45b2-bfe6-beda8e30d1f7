{"name": "backend", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"Error: no test specified\" && exit 1", "test-db": "node test-db-connection.js", "test-admin": "node test-admin.js", "test-imagekit": "node test-imagekit.js", "test-ipinfo": "node test-ipinfo.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "imagekit": "^6.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "replicate": "^1.0.1"}}