import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, HelpCircle, Search, Clock, Shield, Zap, Download, DollarSign, Home, Sparkles, Star, Users, Globe, Mail, MessageCircle, BookOpen, Lightbulb } from 'lucide-react';
import SEO from '../components/SEO';
import Breadcrumbs, { BreadcrumbStructuredData } from '../components/Breadcrumbs';

const FAQ = () => {
  const [openItems, setOpenItems] = useState({});
  const [searchTerm, setSearchTerm] = useState('');

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  const toggleItem = (index) => {
    setOpenItems(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const faqData = [
    {
      category: "Getting Started",
      icon: <Sparkles className="w-5 h-5" />,
      color: "purple",
      questions: [
        {
          question: "What is Gen Free AI?",
          answer: "Gen Free AI is a free AI-powered image generation platform that creates stunning, high-quality images from text descriptions. Simply describe what you want to see, and our advanced AI will generate a unique image for you."
        },
        {
          question: "How do I generate an image?",
          answer: "It's simple! Just go to our Generate page, type a detailed description of what you want to create in the prompt box, and click 'Generate Image'. Our AI will process your request and create a unique image based on your description."
        },
        {
          question: "Is Gen Free AI really free?",
          answer: "Yes! Gen Free AI is completely free to use. There are no hidden fees, subscriptions, or premium tiers. We believe AI creativity should be accessible to everyone."
        },
        {
          question: "Do I need to create an account?",
          answer: "No account required! You can start generating images immediately without any registration. However, please note that your generated images are stored temporarily and will expire after 24 hours."
        }
      ]
    },
    {
      category: "Image Generation",
      icon: <Zap className="w-5 h-5" />,
      color: "blue",
      questions: [
        {
          question: "What makes a good prompt?",
          answer: "Good prompts are detailed and specific. Include information about style, colors, mood, setting, and any specific elements you want. For example: 'A serene mountain landscape at sunset with purple and orange clouds, painted in watercolor style' works better than just 'mountain'."
        },
        {
          question: "How long does it take to generate an image?",
          answer: "Image generation typically takes 10-30 seconds, depending on server load and the complexity of your prompt. Our AI processes your request and creates a high-quality image."
        },
        {
          question: "What image formats are supported?",
          answer: "All generated images are provided in JPG format with a resolution of (1:1,3:4,9:16,4:3,16:9,2:3,3:2)  pixels. JPG format ensures high quality and supports transparency if needed."
        },
        {
          question: "Can I generate multiple images from the same prompt?",
          answer: "Yes! Each time you submit a prompt, our AI generates a unique image. Even with the same prompt, you'll get different creative interpretations each time."
        }
      ]
    },
    {
      category: "Image Storage & Download",
      icon: <Download className="w-5 h-5" />,
      color: "green",
      questions: [
        {
          question: "How long are my images stored?",
          answer: "Generated images are automatically deleted after 24 hours to protect your privacy and manage server storage. We recommend downloading any images you want to keep immediately after generation."
        },
        {
          question: "How do I download my images?",
          answer: "You can download images directly from the generation page or visit your History page to see all recent images. Click the download button or right-click and 'Save image as' to save to your device."
        },
        {
          question: "Can I access my images from different devices?",
          answer: "Images are stored locally in your browser's history. To access images from different devices, you'll need to download them and transfer them manually, or use the share functionality."
        },
        {
          question: "What happens to expired images?",
          answer: "After 24 hours, images are permanently deleted from our servers and cannot be recovered. Make sure to save any images you want to keep before they expire."
        }
      ]
    },
    {
      category: "Privacy & Usage Rights",
      icon: <Shield className="w-5 h-5" />,
      color: "orange",
      questions: [
        {
          question: "Who owns the generated images?",
          answer: "You own the images you generate! Once created, you have full rights to use, modify, and distribute your generated images for personal or commercial purposes."
        },
        {
          question: "Is my data private?",
          answer: "Yes, we take privacy seriously. We don't store your prompts permanently, and images are automatically deleted after 24 hours. We don't track or store personal information."
        },
        {
          question: "Can I use generated images commercially?",
          answer: "Yes! All images generated through Gen Free AI can be used for commercial purposes. You have full rights to the images you create."
        },
        {
          question: "Are there any content restrictions?",
          answer: "We have content policies in place to ensure appropriate use. Please avoid generating harmful, illegal, or inappropriate content. Our AI is designed to refuse inappropriate requests."
        }
      ]
    },
    {
      category: "Service & Monetization",
      icon: <DollarSign className="w-5 h-5" />,
      color: "emerald",
      questions: [
        {
          question: "How do you keep the service free?",
          answer: "We keep Gen Free AI free by displaying third-party advertisements on our website. Advertisement revenue helps us cover server costs, maintenance, and service improvements while keeping the AI image generation completely free for users."
        },
        {
          question: "Why do I see advertisements on the website?",
          answer: "Advertisements are our primary source of revenue to maintain this free service. We carefully select advertising partners to ensure ads are relevant and non-intrusive. Your support through ad engagement helps us continue providing free AI image generation."
        },
        {
          question: "Are the advertisements safe?",
          answer: "We work with reputable advertising networks and partners. However, we recommend exercising normal internet caution when clicking on any advertisements. We are not responsible for the content or practices of third-party advertisers."
        },
        {
          question: "Can I use an ad blocker?",
          answer: "While you can use ad blockers, we kindly ask you to consider disabling them on our site. Advertisement revenue directly supports keeping our AI image generation service free for everyone in the community."
        }
      ]
    },
    {
      category: "Technical Support",
      icon: <MessageCircle className="w-5 h-5" />,
      color: "red",
      questions: [
        {
          question: "What if image generation fails?",
          answer: "If generation fails, try refreshing the page and submitting your prompt again. If problems persist, try simplifying your prompt or contact <NAME_EMAIL> for support."
        },
        {
          question: "Why is my image generation slow?",
          answer: "Generation speed can vary based on server load and prompt complexity. During peak times, it may take longer. We're constantly working to improve our infrastructure for faster generation."
        },
        {
          question: "What browsers are supported?",
          answer: "Gen Free AI works on all modern browsers including Chrome, Firefox, Safari, and Edge. For the best experience, we recommend using the latest version of your preferred browser."
        },
        {
          question: "How do I report a bug or issue?",
          answer: "If you encounter any bugs or issues, please contact <NAME_EMAIL> with details about the problem, including your browser type and any error messages you see."
        }
      ]
    }
  ];

  const filteredFAQs = faqData.map(category => ({
    ...category,
    questions: category.questions.filter(
      item =>
        item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.answer.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })).filter(category => category.questions.length > 0);

  // Create breadcrumbs for FAQ page
  const faqBreadcrumbs = [
    { label: 'Home', href: '/', icon: Home },
    { label: 'FAQ', href: null, icon: HelpCircle }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-8 relative overflow-hidden">
      {/* SEO */}
      <SEO
        title="FAQ - Frequently Asked Questions | Gen Free AI"
        description="Find answers to common questions about Gen Free AI. Learn how to generate images, download them, and understand our privacy policies. Get help with technical issues."
        keywords="Gen Free AI FAQ, frequently asked questions, AI image generator help, how to generate images, image download, privacy policy, technical support"
      />

      {/* Structured Data for Breadcrumbs */}
      <BreadcrumbStructuredData breadcrumbs={faqBreadcrumbs} />

      {/* Background Elements */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden -z-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-purple-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '0s' }}></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-blue-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-40 left-20 w-20 h-20 bg-green-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '4s' }}></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-orange-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '6s' }}></div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Breadcrumbs */}
        <Breadcrumbs customBreadcrumbs={faqBreadcrumbs} />

        {/* Enhanced Header */}
        <div className="text-center mb-16">
          <div className="relative inline-block mb-8 animate-bounce-in">
            <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 dark:from-blue-900 dark:via-purple-900 dark:to-pink-900 rounded-3xl flex items-center justify-center shadow-lg">
              <HelpCircle className="w-10 h-10 sm:w-12 sm:h-12 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="absolute -top-2 -right-2 animate-bounce">
              <Sparkles className="w-6 h-6 text-yellow-500" />
            </div>
          </div>

          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent animate-gradient-x mb-6">
            Frequently Asked Questions
          </h1>

          <p className="text-xl sm:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
            Find answers to common questions about Gen Free AI and get the help you need
          </p>

          {/* Quick Stats */}
          <div className="flex flex-wrap justify-center gap-4">
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-900 to-purple-900 rounded-full shadow-lg">
              <BookOpen className="w-5 h-5 text-blue-400" />
              <span className="font-semibold text-blue-300">6 Categories</span>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-900 to-pink-900 rounded-full shadow-lg">
              <Lightbulb className="w-5 h-5 text-purple-400" />
              <span className="font-semibold text-purple-300">25+ Answers</span>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-green-900 to-emerald-900 rounded-full shadow-lg">
              <Users className="w-5 h-5 text-green-400" />
              <span className="font-semibold text-green-300">Community Driven</span>
            </div>
          </div>
        </div>

        {/* Enhanced Search */}
        <div className="mb-12 animate-fade-in-up">
          <div className="relative max-w-2xl mx-auto">
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
              <Search className="w-6 h-6" />
            </div>
            <input
              type="text"
              placeholder="Search through all FAQs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-6 py-4 border-2 border-gray-600 rounded-2xl
                         bg-gray-800 text-white text-lg
                         focus:ring-2 focus:ring-purple-500 focus:border-purple-500 focus:bg-gray-700
                         transition-all duration-200 shadow-lg hover:shadow-xl"
            />
          </div>
        </div>

        {/* Enhanced FAQ Categories */}
        <div className="space-y-12">
          {filteredFAQs.map((category, categoryIndex) => {
            const getColorClasses = (color) => {
              const colorMap = {
                purple: {
                  bg: 'from-purple-900 to-pink-900',
                  icon: 'from-purple-500 to-pink-500',
                  text: 'from-purple-600 to-pink-600',
                  border: 'border-purple-800'
                },
                blue: {
                  bg: 'from-blue-900 to-cyan-900',
                  icon: 'from-blue-500 to-cyan-500',
                  text: 'from-blue-600 to-cyan-600',
                  border: 'border-blue-800'
                },
                green: {
                  bg: 'from-green-900 to-emerald-900',
                  icon: 'from-green-500 to-emerald-500',
                  text: 'from-green-600 to-emerald-600',
                  border: 'border-green-800'
                },
                orange: {
                  bg: 'from-orange-900 to-red-900',
                  icon: 'from-orange-500 to-red-500',
                  text: 'from-orange-600 to-red-600',
                  border: 'border-orange-800'
                },
                emerald: {
                  bg: 'from-emerald-900 to-teal-900',
                  icon: 'from-emerald-500 to-teal-500',
                  text: 'from-emerald-600 to-teal-600',
                  border: 'border-emerald-800'
                },
                red: {
                  bg: 'from-red-900 to-pink-900',
                  icon: 'from-red-500 to-pink-500',
                  text: 'from-red-600 to-pink-600',
                  border: 'border-red-800'
                }
              };
              return colorMap[color] || colorMap.blue;
            };

            const colors = getColorClasses(category.color);

            return (
              <div key={categoryIndex} className="animate-fade-in-up" style={{ animationDelay: `${categoryIndex * 100}ms` }}>
                {/* Enhanced Category Header */}
                <div className={`bg-gradient-to-r ${colors.bg} ${colors.border} border-2 rounded-3xl p-6 sm:p-8 mb-6 shadow-lg`}>
                  <div className="flex items-center gap-4">
                    <div className={`p-3 bg-gradient-to-r ${colors.icon} rounded-2xl shadow-lg`}>
                      <div className="text-white">
                        {category.icon}
                      </div>
                    </div>
                    <h2 className={`text-3xl sm:text-4xl font-bold bg-gradient-to-r ${colors.text} bg-clip-text text-transparent`}>
                      {category.category}
                    </h2>
                  </div>
                </div>

                {/* Enhanced Questions */}
                <div className="space-y-4">
                  {category.questions.map((item, index) => {
                    const itemKey = `${categoryIndex}-${index}`;
                    const isOpen = openItems[itemKey];

                    return (
                      <div
                        key={index}
                        className="group bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl shadow-lg hover:shadow-2xl border border-gray-600 overflow-hidden transition-all duration-300 hover:scale-[1.02]"
                      >
                        <button
                          onClick={() => toggleItem(itemKey)}
                          className="w-full px-6 sm:px-8 py-6 text-left flex items-center justify-between hover:bg-gradient-to-r hover:from-gray-700 hover:to-gray-600 transition-all duration-200"
                        >
                          <h3 className="text-lg sm:text-xl font-bold text-white pr-4 leading-relaxed">
                            {item.question}
                          </h3>
                          <div className={`p-2 rounded-xl transition-all duration-300 ${isOpen ? 'bg-purple-900' : 'bg-gray-700 group-hover:bg-purple-900'}`}>
                            {isOpen ? (
                              <ChevronUp className="w-5 h-5 text-purple-400 flex-shrink-0" />
                            ) : (
                              <ChevronDown className="w-5 h-5 text-gray-400 group-hover:text-purple-400 flex-shrink-0" />
                            )}
                          </div>
                        </button>

                        {isOpen && (
                          <div className="px-6 sm:px-8 pb-6 border-t border-gray-600 bg-gradient-to-r from-gray-700 to-gray-800">
                            <div className="pt-6">
                              <p className="text-gray-300 leading-relaxed text-base sm:text-lg">
                                {item.answer}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>

        {/* Enhanced No Results */}
        {filteredFAQs.length === 0 && searchTerm && (
          <div className="text-center py-20">
            <div className="relative">
              <div className="w-24 h-24 sm:w-32 sm:h-32 mx-auto bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-3xl flex items-center justify-center mb-8 shadow-lg">
                <Search className="w-12 h-12 sm:w-16 sm:h-16 text-gray-400" />
              </div>
              <div className="absolute -top-2 -right-2 animate-bounce">
                <Sparkles className="w-6 h-6 text-yellow-500" />
              </div>
            </div>

            <h3 className="text-2xl sm:text-3xl font-bold text-white mb-4">
              No results found
            </h3>
            <p className="text-lg text-gray-300 mb-8 max-w-md mx-auto">
              Try searching with different keywords or browse all categories above.
            </p>

            <button
              onClick={() => setSearchTerm('')}
              className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <HelpCircle className="w-5 h-5" />
              Clear Search
            </button>
          </div>
        )}

        {/* Enhanced Contact Section */}
        <div className="mt-20 bg-gradient-to-br from-blue-900/20 via-purple-800/10 to-blue-900/20 border-2 border-blue-800 rounded-3xl p-8 sm:p-12 text-center animate-fade-in-up shadow-2xl">
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl shadow-lg">
              <Mail className="w-8 h-8 text-white" />
            </div>
          </div>

          <h3 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            Still have questions?
          </h3>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Can't find what you're looking for? Our support team is here to help you get the answers you need!
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <Mail className="w-5 h-5" />
              Contact Support
            </a>

            <button
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
              className="inline-flex items-center gap-2 px-6 py-3 bg-gray-800 hover:bg-gray-700 text-gray-300 border-2 border-gray-600 rounded-xl transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <HelpCircle className="w-5 h-5" />
              Back to Top
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FAQ;
