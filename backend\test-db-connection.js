require('dotenv').config();
const mongoose = require('mongoose');

async function testConnection() {
  try {
    console.log('🔗 Testing MongoDB Atlas connection...');
    const mongoURI = process.env.MONGODB_URI;

    if (!mongoURI) {
      console.error('❌ MONGODB_URI environment variable is not set');
      return;
    }

    await mongoose.connect(mongoURI);

    console.log('✅ Successfully connected to MongoDB Atlas!');

    // Test creating a simple document
    const testSchema = new mongoose.Schema({
      test: String,
      createdAt: { type: Date, default: Date.now }
    });

    const TestModel = mongoose.model('Test', testSchema);

    const testDoc = new TestModel({
      test: 'Database connection test'
    });

    await testDoc.save();

    // Clean up test document
    await TestModel.deleteOne({ _id: testDoc._id });

  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

testConnection();
