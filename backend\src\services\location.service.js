const fetch = require('node-fetch');

/**
 * Get location information from IP address using ipinfo.io
 * @param {string} ipAddress - The IP address to lookup
 * @returns {Object} Location information
 */
const getLocationFromIP = async (ipAddress) => {
    try {
        // Handle local/private IPs
        if (!ipAddress || 
            ipAddress === '::1' || 
            ipAddress === '127.0.0.1' || 
            ipAddress.includes('::ffff:127.0.0.1') ||
            ipAddress.includes('::ffff:192.168.') ||
            ipAddress.includes('::ffff:10.') ||
            ipAddress.includes('::ffff:172.')) {
            return {
                country: 'Local',
                countryCode: 'LO',
                city: 'Development',
                region: 'Local',
                ip: ipAddress
            };
        }

        // Clean IPv6 mapped IPv4 addresses
        let cleanIP = ipAddress.replace('::ffff:', '');
        
        // Skip if it's still a local/private IP after cleaning
        if (cleanIP.startsWith('192.168.') || 
            cleanIP.startsWith('10.') || 
            cleanIP.startsWith('172.') ||
            cleanIP === '127.0.0.1') {
            return {
                country: 'Local Network',
                countryCode: 'LO',
                city: 'Private',
                region: 'Local',
                ip: cleanIP
            };
        }

        // Use ipinfo.io API with your token
        const token = '36f88e3db5209c';
        const response = await fetch(`https://api.ipinfo.io/lite/${cleanIP}?token=${token}`, {
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'GenFreeAI/1.0'
            },
            timeout: 5000 // 5 second timeout
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        
        return {
            country: data.country_name || data.country || 'Unknown',
            countryCode: data.country_code || data.country || 'UN',
            city: data.city || '',
            region: data.region || '',
            ip: cleanIP
        };

    } catch (error) {
        console.warn('Could not fetch location data for IP:', ipAddress, error.message);
        
        return {
            country: 'Unknown',
            countryCode: 'UN',
            city: '',
            region: '',
            ip: ipAddress.replace('::ffff:', '')
        };
    }
};

module.exports = {
    getLocationFromIP
};
