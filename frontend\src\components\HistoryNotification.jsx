import React, { useEffect } from 'react';
import { Check, History, X } from 'lucide-react';
import { Link } from 'react-router-dom';

const HistoryNotification = ({
  isVisible,
  onClose,
  onHistoryClick,
  imageCount = 1
}) => {
  useEffect(() => {
    if (isVisible) {
      // Auto-hide after 5 seconds
      const timer = setTimeout(() => {
        onClose();
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [isVisible, onClose]);

  if (!isVisible) return null;

  return (
    <div className="fixed top-4 right-4 z-50 animate-slide-in">
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700
                    rounded-lg shadow-lg p-4 max-w-sm animate-bounce-in">
        <div className="flex items-start gap-3">
          {/* Success Icon */}
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center
                          animate-scale-in">
              <Check className="w-4 h-4 text-green-600 dark:text-green-400 animate-rotate-in" />
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">
              Image Saved to History
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
              {imageCount === 1
                ? 'Your generated image has been saved.'
                : `${imageCount} images saved to your history.`
              }
            </p>

            {/* Action Button */}
            <div className="mt-3 flex gap-2">
              <Link
                to="/history"
                onClick={onHistoryClick}
                className="inline-flex items-center gap-1 px-3 py-1.5 bg-blue-600 hover:bg-blue-700
                         text-white text-xs font-medium rounded-md transition-colors"
              >
                <History className="w-3 h-3" />
                View History
              </Link>
              <button
                onClick={onClose}
                className="inline-flex items-center gap-1 px-3 py-1.5 bg-gray-100 hover:bg-gray-200
                         dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300
                         text-xs font-medium rounded-md transition-colors"
              >
                Dismiss
              </button>
            </div>
          </div>

          {/* Close Button */}
          <button
            onClick={onClose}
            className="flex-shrink-0 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
            title="Close notification"
          >
            <X className="w-4 h-4 text-gray-400 dark:text-gray-500" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default HistoryNotification;
