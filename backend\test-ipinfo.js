require('dotenv').config();

async function testIPInfo() {
    try {
        console.log('🌍 Testing ipinfo.io API integration...');
        
        // Test with a public IP (Google's DNS)
        const testIP = '*******';
        const token = '36f88e3db5209c';
        
        console.log(`📍 Testing IP: ${testIP}`);
        console.log(`🔑 Using token: ${token.substring(0, 8)}...`);
        
        const response = await fetch(`https://api.ipinfo.io/lite/${testIP}?token=${token}`, {
            headers: {
                'Accept': 'application/json',
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        console.log('✅ API Response received!');
        console.log('📊 Location Data:');
        console.log(`   Country: ${data.country_name || data.country || 'Unknown'}`);
        console.log(`   Country Code: ${data.country_code || data.country || 'Unknown'}`);
        console.log(`   City: ${data.city || 'Unknown'}`);
        console.log(`   Region: ${data.region || 'Unknown'}`);
        console.log(`   IP: ${data.ip || testIP}`);
        
        console.log('\n🎯 Full Response:');
        console.log(JSON.stringify(data, null, 2));
        
        console.log('\n🚀 ipinfo.io integration is working correctly!');
        console.log('💡 This data will now be displayed in your AdminPanel for each generated image.');
        
    } catch (error) {
        console.error('❌ ipinfo.io test failed:', error.message);
        console.log('\n🔧 Troubleshooting:');
        console.log('1. Check your internet connection');
        console.log('2. Verify the API token is correct');
        console.log('3. Check if you have reached the API rate limit');
        console.log('4. Ensure the ipinfo.io service is accessible');
    }
}

testIPInfo();
