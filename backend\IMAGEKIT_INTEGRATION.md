# ImageKit Integration for Permanent Image Storage

## Overview

The AI service has been enhanced to automatically upload generated images to ImageKit for permanent storage. This solves the issue of Replicate's temporary URLs that expire after 1 hour.

## How It Works

### Before (Original Flow)
1. Generate image using Replicate API
2. Return temporary Replicate URL (expires in 1 hour)
3. Images become inaccessible after expiration

### After (Enhanced Flow)
1. Generate image using Replicate API
2. Get temporary Replicate URL
3. **Upload image to ImageKit using the temporary URL**
4. **Return permanent ImageKit URL**
5. Images remain accessible indefinitely

## Implementation Details

### Modified Files

1. **`src/services/ai.service.js`** - Enhanced with ImageKit integration
2. **`.env.example`** - Added ImageKit environment variables
3. **`README.md`** - Updated with ImageKit setup instructions
4. **`package.json`** - Added test scripts
5. **`test-imagekit.js`** - New test script for ImageKit connection

### Key Features

- **Automatic Upload**: Images are automatically uploaded to ImageKit after generation
- **Organized Storage**: Images are stored in `/ai-generated-images` folder
- **Rich Metadata**: Each image includes prompt, aspect ratio, generation timestamp
- **Fallback Mechanism**: If ImageKit upload fails, falls back to Replicate URL
- **Unique Filenames**: Timestamp-based naming prevents conflicts
- **Tagging System**: Images are tagged for easy organization

### Environment Variables Required

Add these to your `.env` file:

```env
IMAGEKIT_PUBLIC_KEY=your_imagekit_public_key
IMAGEKIT_PRIVATE_KEY=your_imagekit_private_key
IMAGEKIT_URL_ENDPOINT=https://ik.imagekit.io/your_imagekit_id/
```

## Setup Instructions

### 1. Get ImageKit Credentials

1. Sign up at [ImageKit.io](https://imagekit.io/)
2. Go to your Dashboard → Developer section
3. Copy your Public Key, Private Key, and URL Endpoint

### 2. Configure Environment

1. Copy `.env.example` to `.env`
2. Add your ImageKit credentials
3. Ensure all other environment variables are set

### 3. Test the Integration

```bash
# Test ImageKit connection
npm run test-imagekit

# Test the full AI service
npm run dev
```

## Benefits

1. **Permanent Storage**: Images never expire
2. **Better Performance**: ImageKit's global CDN for faster loading
3. **Image Optimization**: Automatic optimization and transformations
4. **Better Organization**: Structured storage with metadata
5. **Scalability**: Handle large volumes of images efficiently

## Error Handling

- If ImageKit upload fails, the service falls back to the original Replicate URL
- Detailed logging helps with debugging
- Graceful degradation ensures the service continues working

## File Organization

Images are stored with the following structure:
```
/ai-generated-images/
├── ai-generated-1703123456789-1x1.jpg
├── ai-generated-1703123456790-16x9.jpg
└── ...
```

## Metadata Stored

Each image includes:
- Original prompt
- Aspect ratio
- Generation timestamp
- Source (replicate-flux)
- Tags for categorization

## Testing

Use the provided test script to verify your setup:

```bash
npm run test-imagekit
```

This will test:
- Environment variable configuration
- ImageKit authentication
- URL generation capabilities

## Troubleshooting

### Common Issues

1. **Missing Environment Variables**
   - Ensure all ImageKit variables are set in `.env`
   - Check variable names match exactly

2. **Authentication Errors**
   - Verify your ImageKit credentials are correct
   - Check your ImageKit account is active

3. **Upload Failures**
   - Check your ImageKit storage quota
   - Verify network connectivity
   - Review ImageKit dashboard for error logs

### Debug Logs

The service provides detailed console logs:
- `🎨 Generated image from Replicate:` - Shows Replicate URL
- `📤 Uploading to ImageKit...` - Upload start
- `✅ Image uploaded to ImageKit successfully` - Upload success
- `🔗 ImageKit URL:` - Final permanent URL
- `❌ ImageKit upload failed:` - Upload errors
- `⚠️ Falling back to Replicate URL` - Fallback activation

## Next Steps

1. Set up your ImageKit account
2. Configure environment variables
3. Test the integration
4. Deploy and enjoy permanent image storage!
