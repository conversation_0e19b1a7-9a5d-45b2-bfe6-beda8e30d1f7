# Gen Free AI Frontend

React-based frontend for the Gen Free AI image generation platform.

## Features

- 🎨 AI Image Generation Interface
- 📱 Responsive Design
- 🌙 Dark Theme Design
- 📊 Image History Management
- 🔗 Social Media Integration
- ⚡ Fast Performance with Vite
- 🎯 SEO Optimized

## Tech Stack

- **React 19** - UI Framework
- **Vite** - Build Tool & Dev Server
- **Tailwind CSS** - Styling Framework
- **React Router** - Client-side Routing
- **Lucide React** - Icon Library
- **Axios** - HTTP Client

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start development server:**
   ```bash
   npm run dev
   ```

3. **Build for production:**
   ```bash
   npm run build
   ```

4. **Preview production build:**
   ```bash
   npm run preview
   ```

## Project Structure

```
frontend/
├── public/           # Static assets
├── src/
│   ├── components/   # Reusable UI components
│   ├── pages/        # Page components
│   ├── utils/        # Utility functions
│   ├── hooks/        # Custom React hooks
│   ├── contexts/     # React contexts
│   └── assets/       # Images and other assets
├── index.html        # HTML template
└── vite.config.js    # Vite configuration
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Environment Setup

The frontend connects to the backend API. Make sure the backend is running on the expected port (default: 8000).

## Features Overview

### Image Generation
- Text-to-image AI generation
- Multiple aspect ratio options
- Random prompt suggestions
- Real-time generation status

### History Management
- Local storage of generated images
- Search and filter capabilities
- Bulk operations
- Export functionality

### User Interface
- Clean, modern design
- Mobile-responsive layout
- Accessibility features
- Performance optimized

## Contributing

1. Follow the existing code style
2. Use meaningful component and variable names
3. Add comments for complex logic
4. Test your changes thoroughly

## License

This project is part of the Gen Free AI platform.
